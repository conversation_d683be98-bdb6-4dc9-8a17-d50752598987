/* Updated mortar.css for button colors and styling */
/* General Styles */
body {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin: 0;
    padding: calc(var(--spacing-unit) * 4); /* 32px padding */
    font-family: var(--font-family-ui); /* Using variables from style.css */
    background-color: var(--background-light); /* Using variables from style.css */
    color: var(--text-dark); /* Using variables from style.css */
    line-height: 1.6;
}

.container {
    width: 95%;
    max-width: 1200px;
    margin-bottom: calc(var(--spacing-unit) * 4); /* Add space at bottom */
}

h1 {
    margin-bottom: calc(var(--spacing-unit) * 3); /* 24px margin */
    color: var(--primary-color); /* Using variables from style.css */
    text-align: center;
    font-weight: 700;
    font-size: 2.5em;
}

.intro {
    text-align: center;
    color: var(--secondary-color); /* Using variables from style.css */
    margin-bottom: calc(var(--spacing-unit) * 4); /* 32px margin */
    font-size: 1em;
}

/* Map Styles */
#mortar-map-container {
     width: 100%;
    margin-bottom: calc(var(--spacing-unit) * 4); /* 32px margin */
}

#mortar-map {
    height: 500px; /* Slightly increased map height */
    width: 100%;
    border: 1px solid var(--border-color); /* Using variables from style.css */
    border-radius: var(--border-radius-lg);
    box-shadow: var(--box-shadow-large);
    position: relative;
    z-index: 1;
    overflow: hidden;
}

#mortar-map::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(to right, var(--military-olive-dark), var(--military-olive-light));
    z-index: 10;
    opacity: 0.8;
}

/* Adjust Leaflet zoom control position */
.leaflet-control-zoom {
    margin-left: var(--spacing-unit) !important; /* 8px margin */
    margin-top: var(--spacing-unit) !important; /* 8px margin */
     border-radius: var(--border-radius) !important; /* Consistent border radius */
     overflow: hidden; /* Hide sharp corners if border-radius applied */
}

.leaflet-control-zoom a {
    color: var(--text-dark) !important; /* Dark text color */
}


/* --- Controls Container Styles --- */
#mortar-controls-container {
    display: flex;
    flex-direction: column;
    gap: calc(var(--spacing-unit) * 4); /* 32px gap */
    background-color: var(--surface-color); /* Use surface color */
    background-image: var(--grid-pattern);
    padding: calc(var(--spacing-unit) * 4.5); /* 36px padding */
    border: 1px solid var(--border-color);
    border-left: 4px solid var(--military-olive);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--box-shadow-large);
    width: 100%; /* Ensure it takes full width of container */
    box-sizing: border-box;
    position: relative;
    overflow: hidden;
}

#mortar-controls-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(to bottom, rgba(255, 255, 255, 0.7), rgba(255, 255, 255, 0.3));
    pointer-events: none;
    opacity: 0.5;
}

section {
    padding-bottom: calc(var(--spacing-unit) * 3); /* 24px padding bottom */
    border-bottom: 1px solid var(--border-color);
    position: relative;
}

section::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 80px;
    height: 1px;
    background: linear-gradient(to right, var(--military-olive), transparent);
}

section:last-child {
    border-bottom: none;
    padding-bottom: 0;
}

section:last-child::after {
    display: none;
}

h3 {
    color: var(--primary-color);
    text-align: center;
    margin-bottom: calc(var(--spacing-unit) * 3); /* 24px margin below headings */
    font-weight: 600; /* Semi-bold for headings */
    font-size: 1.4em;
    position: relative;
    padding-bottom: calc(var(--spacing-unit) * 1.5);
}

h3::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 60px;
    height: 3px;
    background: linear-gradient(to right, var(--military-olive-light), var(--military-olive));
    border-radius: 2px;
}


.form-group {
    margin-bottom: calc(var(--spacing-unit) * 2); /* 16px margin */
    position: relative;
}

.form-group::after {
    content: '';
    position: absolute;
    bottom: -12px;
    left: 0;
    width: 40px;
    height: 2px;
    background: linear-gradient(to right, var(--military-olive-light), transparent);
    opacity: 0.5;
}

label {
    display: block;
    margin-bottom: var(--spacing-unit); /* 8px margin */
    font-weight: 500;
    color: var(--text-dark);
    font-size: 1em;
    position: relative;
    padding-left: 12px;
}

label::before {
    content: '';
    position: absolute;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
    width: 4px;
    height: 14px;
    background: var(--military-olive-light);
    opacity: 0.6;
    border-radius: 2px;
}

select,
input[type="number"],
input[type="text"] {
    width: calc(100% - calc(var(--spacing-unit) * 3)); /* Adjust width for padding and border */
    padding: calc(var(--spacing-unit) * 1.5); /* 12px padding */
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    box-sizing: border-box;
    font-size: 1em;
    color: var(--text-dark);
    background-color: var(--background-light);
    transition: border-color var(--transition-speed) ease, box-shadow var(--transition-speed) ease;
}

select:focus,
input[type="number"]:focus,
input[type="text"]:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 5px rgba(var(--primary-color), 0.5); /* Use primary color for focus glow */
}


button:not(.collapsible) {
    color: var(--text-light);
    padding: calc(var(--spacing-unit) * 1.5) calc(var(--spacing-unit) * 3); /* 12px 24px padding */
    border: none;
    border-radius: var(--border-radius);
    cursor: pointer;
    font-size: 1em;
    transition: background-color var(--transition-speed) ease, transform 0.2s var(--ease-out-quad), box-shadow var(--transition-speed) ease;
    box-shadow: var(--box-shadow-subtle);
    font-weight: 500;
}

button:not(.collapsible):hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

button:not(.collapsible):active:not(:disabled) {
    transform: translateY(0);
    box-shadow: var(--box-shadow-subtle);
     animation: none; /* Remove glow on click, will add via JS class */
}

button:not(.collapsible):disabled {
    background-color: #cccccc; /* Gray for disabled */
    color: #666666;
    cursor: not-allowed;
    box-shadow: none;
    transform: none;
    opacity: 0.7;
}


/* Specific Button Colors (Defined in style.css for consistency) */
/* These rules are kept here for clarity but should ideally be defined in style.css
   or a shared button component if using a framework. */
#refresh-targets-button {
    background-color: var(--primary-color); /* Dark blue for refresh */
}
#refresh-targets-button:hover:not(:disabled) {
     background-color: #1a2533; /* Darker blue on hover */
}
#refresh-targets-button.button-glow:active:not(:disabled) {
     animation: glow-primary 0.5s ease-in-out infinite alternate; /* Use specific glow for primary */
}
#refresh-targets-button:disabled {
     background-color: #a0cfff; /* Lighter blue when disabled */
     cursor: not-allowed;
     box-shadow: none;
     transform: none;
}

#clear-targets-button-mortar {
    background-color: var(--danger-color); /* Red for clear */
}
#clear-targets-button-mortar:hover:not(:disabled) {
    background-color: #c0392b; /* Darker red on hover */
}
#clear-targets-button-mortar.button-glow:active:not(:disabled) {
    animation: glow-danger 0.5s ease-in-out infinite alternate; /* Use specific glow for danger */
}

#calculate-solution-button {
    background-color: var(--accent-color); /* Emerald green for calculate */
}
#calculate-solution-button:hover:not(:disabled) {
     background-color: #1e8449; /* Darker green on hover */
}
#calculate-solution-button.button-glow:active:not(:disabled) {
     animation: glow-green 0.5s ease-in-out infinite alternate; /* Use specific glow for green */
}
#calculate-solution-button:disabled {
    background-color: #d4edda; /* Lighter green when disabled */
    color: #155724;
    cursor: not-allowed;
    border-color: #c3e6cb;
    box-shadow: none;
    transform: none;
}


.button-group { /* Used for Go To and Remove on target list items */
    display: flex;
    gap: var(--spacing-unit); /* 8px gap */
    justify-content: flex-end; /* Align to the right in list items */
    flex-wrap: wrap;
}

.button-group button { /* Styling for buttons within a button-group */
    padding: var(--spacing-unit) calc(var(--spacing-unit) * 1.5); /* 8px 12px padding */
    font-size: 0.85em;
    margin: 0; /* Remove default button margin */
}


/* --- Mortar Position Styles --- */
#mortar-settings label {
    display: block;
    margin-bottom: var(--spacing-unit); /* 8px margin */
    font-weight: 500;
    color: var(--text-dark);
    font-size: 1em;
}

#current-mortar-pos {
    padding: calc(var(--spacing-unit) * 1.5); /* 12px padding */
    background-color: var(--background-light);
    background-image: var(--grid-pattern);
    border: 1px solid var(--border-color);
    border-left: 3px solid var(--military-olive);
    border-radius: var(--border-radius);
    font-family: var(--font-family-data); /* Use data font for coordinates */
    font-size: 1em;
    text-align: center;
    color: var(--text-dark);
    box-shadow: var(--box-shadow-subtle);
    position: relative;
    overflow: hidden;
}

#current-mortar-pos::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(to bottom, rgba(255, 255, 255, 0.7), rgba(255, 255, 255, 0.3));
    pointer-events: none;
    opacity: 0.5;
}

/* --- Weapon Selection Styles --- */
.weapon-selection {
    display: flex;
    flex-wrap: wrap;
    gap: calc(var(--spacing-unit) * 3); /* 24px gap */
    align-items: flex-start;
}

.form-group { /* Applied to weapon select items */
    margin-bottom: 0; /* Remove bottom margin from form-group in flex container */
    flex: 1;
    min-width: 150px;
}

/* --- Calculation Controls Styles --- */
.calculation-controls {
    text-align: center;
}

#calculated-solution-display {
    margin-top: calc(var(--spacing-unit) * 3); /* 24px margin */
    padding: calc(var(--spacing-unit) * 3); /* 24px padding */
    background-color: var(--background-light);
    background-image: var(--grid-pattern);
    border: 1px solid var(--border-color);
    border-left: 4px solid var(--military-olive);
    border-radius: var(--border-radius);
    text-align: left;
    min-height: 120px;
    font-size: 1em;
    line-height: 1.6;
    color: var(--text-dark);
    box-shadow: var(--box-shadow-medium);
    position: relative;
    transition: all var(--transition-speed) ease;
    font-family: var(--font-family-data); /* Use data font for solution display */
    overflow: hidden;
}

#calculated-solution-display::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(to bottom, rgba(255, 255, 255, 0.7), rgba(255, 255, 255, 0.3));
    pointer-events: none;
    opacity: 0.5;
}

#calculated-solution-display h3 {
    margin-top: 0;
    margin-bottom: calc(var(--spacing-unit) * 2); /* 16px margin */
    text-align: center;
    color: var(--primary-color);
    font-size: 1.3em;
    font-weight: 600;
}
#calculated-solution-display p {
    margin: var(--spacing-unit) 0; /* 8px margin */
}
#calculated-solution-display hr {
    margin: calc(var(--spacing-unit) * 2.5) 0; /* 20px margin */
    border: 0;
    border-top: 1px solid #ccc;
}

#calculated-solution-display.calculated {
    background-color: #d4edda; /* Light green */
    border-color: #c3e6cb; /* Lighter green */
}

#calculated-solution-display .calculation-confirmation {
    position: absolute;
    top: calc(var(--spacing-unit) * 5); /* Move down from 8px to 40px to avoid header overlap */
    right: var(--spacing-unit); /* 8px from right */
    font-size: 0.8em;
    color: green;
    font-weight: bold;
}


/* --- Target List Item Styling & Animations --- */
#target-list-items li, #received-targets-list li {
    background-color: var(--surface-color); /* White background for list items */
    border: 1px solid var(--border-color);
    border-left: 3px solid var(--military-olive);
    margin-bottom: calc(var(--spacing-unit) * 1.5); /* Space between list items */
    border-radius: var(--border-radius); /* Rounded corners */
    padding: calc(var(--spacing-unit) * 2); /* 16px padding */
    box-shadow: var(--box-shadow-subtle); /* Subtle shadow */
    transition: all 0.2s ease;
    position: relative;
    overflow: hidden;
}

#target-list-items li::before, #received-targets-list li::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(to bottom, rgba(255, 255, 255, 0.1), transparent);
    pointer-events: none;
}

#target-list-items li:last-child, #received-targets-list li:last-child {
    margin-bottom: 0; /* No bottom margin for last item */
}

#target-list-items li:hover:not(.selected), #received-targets-list li:hover:not(.selected) {
    background-color: var(--background-light);
    border-color: var(--border-color-hover);
    border-left-color: var(--military-olive-light);
    transform: translateY(-2px);
    box-shadow: var(--box-shadow-medium);
}

#target-list-items li.selected, #received-targets-list li.selected {
    background-color: rgba(224, 247, 250, 0.5); /* Light cyan highlight with transparency */
    font-weight: bold;
    border-color: var(--military-olive-light);
    border-left-color: var(--military-olive);
    box-shadow: var(--box-shadow-medium);
}

/* --- Received Targets Area Styles --- */
#target-display-area {
    width: 100%; /* Ensure it takes full width of container */
    box-sizing: border-box;
}

#target-display-area h2 {
    text-align: center;
    margin-top: 0;
    margin-bottom: calc(var(--spacing-unit) * 3); /* 24px margin */
    color: var(--primary-color);
    font-weight: 600;
    font-size: 1.8em;
}

.target-list-controls {
    text-align: center;
    margin-bottom: calc(var(--spacing-unit) * 3); /* 24px margin */
}

#received-targets-list {
    list-style-type: none;
    padding: 0;
    margin: 0;
    max-height: 350px;
    overflow-y: auto;
    /* Removed border and background here, handled by list item styling */
}


/* Marker styles (Copied from Observer style.css for consistency) */
.target-marker {
    display: flex;
    justify-content: center;
    align-items: center;
    transition: transform 0.3s ease-in-out; /* Smooth transition for marker effects */
}

.target-marker.selected {
    transform: scale(1.2); /* Example: Slightly enlarge selected marker */
    /* Add outline or other visual cues here if needed */
}

/* Marker pulse animation */
@keyframes marker-pulse {
    0% { box-shadow: 0 0 0 0 rgba(255, 255, 0, 0.7); }
    70% { box-shadow: 0 0 0 10px rgba(255, 255, 0, 0); }
    100% { box-shadow: 0 0 0 0 rgba(255, 255, 0, 0); }
}

.target-marker.pulse {
    animation: marker-pulse 1s infinite;
}


/* Loading state styling */
.loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(255, 255, 255, 0.7);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 10;
    font-size: 1.2em;
    color: var(--primary-color);
}

/* Basic Spinner (can be replaced with a CSS spinner) */
.loading-overlay::after {
    content: '';
    border: 4px solid #f3f3f3;
    border-top: 4px solid var(--primary-color);
    border-radius: 50%;
    width: 30px;
    height: 30px;
    animation: spin 1s linear infinite;
    margin-left: 10px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}


/* Inline Error Message Styling */
.feedback-message {
    margin-top: var(--spacing-unit) * 1.5; /* 12px margin */
    font-size: 0.9em;
    min-height: 1.2em; /* Reserve space */
    text-align: center;
}

.feedback-message.success {
    color: var(--accent-color); /* Green */
    font-weight: 500;
}

.feedback-message.error {
    color: var(--danger-color); /* Red */
    font-weight: 500;
}


/* --- Responsive Design --- */
@media (min-width: 768px) {
    #controls-container, #mortar-controls-container {
        flex-direction: row;
        flex-wrap: wrap;
        justify-content: space-between;
        gap: calc(var(--spacing-unit) * 4); /* Increased gap on wider screens */
    }

    #target-information, #mortar-settings {
        width: 48%;
    }

    #grid-and-target-list, #calculation-area {
        width: 48%;
    }

    #add-target-button,
    #send-targets-button,
    #refresh-targets-button,
    #clear-targets-button,
    #clear-targets-button-mortar { /* Adjust margins for wider screens */
        margin: var(--spacing-unit);
    }
}

@media (max-width: 767px) {
     body {
         padding: calc(var(--spacing-unit) * 2); /* 16px padding */
     }
     h1 {
         font-size: 2em;
         margin-bottom: calc(var(--spacing-unit) * 2);
     }
    .intro {
        margin-bottom: calc(var(--spacing-unit) * 3);
    }
     .collapsible {
         padding: var(--spacing-unit);
         font-size: 0.9em;
     }
    .content {
        padding: var(--spacing-unit);
    }
     #map, #mortar-map {
         height: 350px;
     }
     #controls-container, #mortar-controls-container {
         padding: calc(var(--spacing-unit) * 3);
     }
    section {
         padding-bottom: calc(var(--spacing-unit) * 2);
     }

    .form-group {
        margin-bottom: calc(var(--spacing-unit) * 1.5);
    }
     label {
         margin-bottom: var(--spacing-unit) * 0.5;
     }
    select, input[type="number"], input[type="text"] {
        padding: var(--spacing-unit);
    }
    button {
        padding: var(--spacing-unit) calc(var(--spacing-unit) * 1.5);
         font-size: 0.9em;
    }
    .button-group {
        gap: var(--spacing-unit);
    }
     #add-target-button, #send-targets-button,
     #refresh-targets-button, #clear-targets-button, #clear-targets-button-mortar {
         margin: var(--spacing-unit) auto;
     }
    #calculated-solution-display {
         padding: calc(var(--spacing-unit) * 2);
         margin-top: calc(var(--spacing-unit) * 1.5);
     }
     #calculated-solution-display h3 {
         margin-bottom: calc(var(--spacing-unit) * 1.5);
     }
    #calculated-solution-display p {
         margin: var(--spacing-unit) * 0.75 0;
     }
     #calculated-solution-display hr {
         margin: calc(var(--spacing-unit) * 2) 0;
     }
    /* Target display area is now inside a control panel */
    .target-list-controls {
         text-align: center; /* Ensure controls are centered */
         margin-bottom: calc(var(--spacing-unit) * 2);
     }
    #received-targets-list {
        padding: var(--spacing-unit) * 1.5;
    }
    #received-targets-list li {
        padding: var(--spacing-unit) var(--spacing-unit) * 0.75;
        gap: var(--spacing-unit) * 1.5;
    }
}
